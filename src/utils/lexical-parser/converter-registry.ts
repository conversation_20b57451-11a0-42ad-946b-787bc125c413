/**
 * Registry for node converters in the Lexical-Markdown parser system
 */

import { NodeConverter } from './types';

/**
 * Registry for managing node converters
 */
export class ConverterRegistry {
  private static converters: Map<string, NodeConverter> = new Map();
  private static fallbackConverter: NodeConverter | null = null;

  // Performance optimization: cache for canHandle lookups
  private static canHandleCache: Map<string, NodeConverter | null> = new Map();

  // Performance metrics
  private static metrics = {
    lookups: 0,
    cacheHits: 0,
    canHandleCalls: 0
  };

  /**
   * Register a converter for a specific node type
   * @param nodeType - The node type to handle
   * @param converter - The converter instance
   */
  static register(nodeType: string, converter: NodeConverter): void {
    this.converters.set(nodeType, converter);
    // Clear cache when new converters are registered
    this.canHandleCache.clear();
  }

  /**
   * Register a fallback converter for unknown node types
   * @param converter - The fallback converter instance
   */
  static registerFallback(converter: NodeConverter): void {
    this.fallbackConverter = converter;
  }

  /**
   * Get a converter for a specific node type
   * @param nodeType - The node type to find a converter for
   * @returns The converter instance or null if not found
   */
  static getConverter(nodeType: string): NodeConverter | null {
    this.metrics.lookups++;

    // First try to find a specific converter
    const converter = this.converters.get(nodeType);
    if (converter) {
      this.metrics.cacheHits++;
      return converter;
    }

    // Check cache for canHandle lookups
    if (this.canHandleCache.has(nodeType)) {
      this.metrics.cacheHits++;
      return this.canHandleCache.get(nodeType) || null;
    }

    // Try to find a converter that can handle this type
    for (const [, conv] of this.converters) {
      this.metrics.canHandleCalls++;
      if (conv.canHandle(nodeType)) {
        // Cache the result for future lookups
        this.canHandleCache.set(nodeType, conv);
        return conv;
      }
    }

    // Cache null result to avoid repeated expensive lookups
    this.canHandleCache.set(nodeType, null);

    // Return fallback converter if available
    return this.fallbackConverter;
  }

  /**
   * Get all registered converter types
   * @returns Array of registered node types
   */
  static getRegisteredTypes(): string[] {
    return Array.from(this.converters.keys());
  }

  /**
   * Check if a converter is registered for a node type
   * @param nodeType - The node type to check
   * @returns True if a converter is registered
   */
  static hasConverter(nodeType: string): boolean {
    return this.converters.has(nodeType) ||
           Array.from(this.converters.values()).some(conv => conv.canHandle(nodeType)) ||
           this.fallbackConverter !== null;
  }

  /**
   * Unregister a converter for a node type
   * @param nodeType - The node type to unregister
   */
  static unregister(nodeType: string): void {
    this.converters.delete(nodeType);
  }

  /**
   * Clear all registered converters
   */
  static clear(): void {
    this.converters.clear();
    this.fallbackConverter = null;
    this.canHandleCache.clear();
    this.resetMetrics();
  }

  /**
   * Get all registered converters
   * @returns Map of all converters
   */
  static getAllConverters(): Map<string, NodeConverter> {
    return new Map(this.converters);
  }

  /**
   * Register default converters
   */
  static registerDefaults(): void {
    // This is now handled by the index.ts file's initializeLexicalParser function
    // to avoid circular dependencies
  }

  /**
   * Get performance metrics
   * @returns Performance metrics object
   */
  static getMetrics(): {
    lookups: number;
    cacheHits: number;
    canHandleCalls: number;
    cacheHitRate: number;
    cacheSize: number;
  } {
    return {
      ...this.metrics,
      cacheHitRate: this.metrics.lookups > 0 ? this.metrics.cacheHits / this.metrics.lookups : 0,
      cacheSize: this.canHandleCache.size
    };
  }

  /**
   * Reset performance metrics
   */
  static resetMetrics(): void {
    this.metrics = {
      lookups: 0,
      cacheHits: 0,
      canHandleCalls: 0
    };
  }

  /**
   * Clear performance caches
   */
  static clearCaches(): void {
    this.canHandleCache.clear();
  }
}
