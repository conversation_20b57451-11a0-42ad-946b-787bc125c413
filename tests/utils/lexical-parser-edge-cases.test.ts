/**
 * Edge case tests for the Lexical-Markdown parser system
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  LexicalMarkdownParser,
  LexicalDocument,
  initializeLexicalParser
} from '../../src/utils/lexical-parser';

describe('Lexical Parser Edge Cases Tests', () => {
  beforeEach(() => {
    // Ensure parser is initialized
    initializeLexicalParser();
  });

  describe('Malformed Markdown Content', () => {
    it('should handle unclosed code blocks', () => {
      const malformedMarkdown = '```javascript\nconsole.log("unclosed");';
      const result = LexicalMarkdownParser.markdownToLexical(malformedMarkdown);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle unclosed emphasis and strong formatting', () => {
      const testCases = [
        'This has unclosed *emphasis',
        'This has unclosed **strong',
        'Mixed *emphasis and **strong formatting',
        'Multiple ***unclosed formatting'
      ];

      for (const markdown of testCases) {
        const result = LexicalMarkdownParser.markdownToLexical(markdown);
        expect(result.success).toBe(true);
        expect(result.data?.root.children.length).toBeGreaterThan(0);
      }
    });

    it('should handle malformed links', () => {
      const malformedLinks = [
        '[Unclosed link text',
        '[Link](unclosed url',
        '[](empty url)',
        '[Empty text]()',
        '[][]',
        '[Link with spaces in url]( invalid url )'
      ];

      for (const markdown of malformedLinks) {
        const result = LexicalMarkdownParser.markdownToLexical(markdown);
        expect(result.success).toBe(true);
        expect(result.data?.root.children.length).toBeGreaterThan(0);
      }
    });

    it('should handle malformed images', () => {
      const malformedImages = [
        '![Unclosed alt text',
        '![Alt](unclosed url',
        '![]()',
        '![]',
        '![Alt with spaces]( invalid url )',
        '![](data:invalid)'
      ];

      for (const markdown of malformedImages) {
        const result = LexicalMarkdownParser.markdownToLexical(markdown);
        expect(result.success).toBe(true);
        expect(result.data?.root.children.length).toBeGreaterThan(0);
      }
    });

    it('should handle mixed line endings', () => {
      const mixedLineEndings = 'Line 1\r\nLine 2\rLine 3\nLine 4';
      const result = LexicalMarkdownParser.markdownToLexical(mixedLineEndings);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle excessive whitespace', () => {
      const excessiveWhitespace = 'Para 1\n\n\n\n\n\n\n\nPara 2\t\t\t\t\nPara 3';
      const result = LexicalMarkdownParser.markdownToLexical(excessiveWhitespace);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle null bytes and control characters', () => {
      const problematicContent = 'Text with\0null\x01byte\x02and\x03control\x04chars';
      const result = LexicalMarkdownParser.markdownToLexical(problematicContent);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });
  });

  describe('Malformed Lexical Content', () => {
    it('should handle nodes with missing required properties', () => {
      const malformedDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [
            {
              type: 'text',
              // Missing required text property
            } as any,
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Valid text',
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  version: 1
                }
              ]
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(malformedDoc);
      // Should handle gracefully, either with warnings or fallback
      expect(result.success).toBe(true);
    });

    it('should handle nodes with invalid formatting flags', () => {
      const invalidFormattingDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Text with invalid format',
              detail: 0,
              format: 999999, // Invalid format value
              mode: 'normal',
              style: '',
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(invalidFormattingDoc);
      expect(result.success).toBe(true);
      expect(result.data).toContain('Text with invalid format');
    });

    it('should handle nodes with circular references', () => {
      // Create a node with circular reference
      const circularNode: any = {
        type: 'paragraph',
        children: [],
        direction: 'ltr',
        format: '',
        indent: 0,
        version: 1
      };
      
      // Create circular reference
      circularNode.children = [circularNode];

      const circularDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [circularNode],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(circularDoc);
      expect(result.success).toBe(true);
      // Should handle circular reference gracefully
    });

    it('should handle nodes with invalid children arrays', () => {
      const invalidChildrenDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                null,
                undefined,
                {
                  type: 'text',
                  text: 'Valid text',
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  version: 1
                },
                null
              ] as any,
              direction: 'ltr',
              format: '',
              indent: 0,
              version: 1
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(invalidChildrenDoc);
      expect(result.success).toBe(true);
      expect(result.data).toContain('Valid text');
    });
  });

  describe('Unicode and Special Characters', () => {
    it('should handle various unicode characters', () => {
      const unicodeContent = `# Unicode Test 🚀

Chinese: 你好世界
Arabic: مرحبا بالعالم
Russian: Привет мир
Japanese: こんにちは世界
Emoji: 🌍 🎉 💻 ✨ 🔥

Mathematical symbols: ∑ ∫ ∞ ≠ ≤ ≥
Currency: $ € £ ¥ ₹ ₿

Special quotes: "smart quotes" 'apostrophes'
Dashes: – — ‒ ―`;

      const result = LexicalMarkdownParser.markdownToLexical(unicodeContent);
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);

      // Test round-trip
      const backResult = LexicalMarkdownParser.lexicalToMarkdown(result.data!);
      expect(backResult.success).toBe(true);
      expect(backResult.data).toContain('🚀');
      expect(backResult.data).toContain('你好世界');
    });

    it('should handle zero-width characters', () => {
      const zeroWidthContent = 'Text\u200Bwith\u200Czero\u200Dwidth\uFEFFcharacters';
      const result = LexicalMarkdownParser.markdownToLexical(zeroWidthContent);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle right-to-left text', () => {
      const rtlContent = `# Mixed Direction Text

English text followed by Arabic: مرحبا بالعالم

Hebrew: שלום עולם

Mixed: Hello مرحبا שלום World`;

      const result = LexicalMarkdownParser.markdownToLexical(rtlContent);
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });
  });

  describe('Performance Edge Cases', () => {
    it('should handle very long single lines', () => {
      const longLine = 'A'.repeat(100000); // 100KB single line
      const result = LexicalMarkdownParser.markdownToLexical(longLine);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle many small elements', () => {
      // Create markdown with many small elements
      const manyElements = Array.from({ length: 1000 }, (_, i) => `**Bold ${i}**`).join(' ');
      const result = LexicalMarkdownParser.markdownToLexical(manyElements);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle deeply nested quotes', () => {
      let nestedQuotes = 'Original text';
      for (let i = 0; i < 20; i++) {
        nestedQuotes = `> ${nestedQuotes}`;
      }
      
      const result = LexicalMarkdownParser.markdownToLexical(nestedQuotes);
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });
  });

  describe('Content Boundary Cases', () => {
    it('should handle empty and whitespace-only content', () => {
      const testCases = ['', '   ', '\n\n\n', '\t\t\t', ' \n \t \n '];
      
      for (const content of testCases) {
        const result = LexicalMarkdownParser.markdownToLexical(content);
        expect(result.success).toBe(true);
        expect(result.data?.root.children).toHaveLength(0);
      }
    });

    it('should handle content with only special characters', () => {
      const specialOnly = '!@#$%^&*()_+-={}[]|\\:";\'<>?,./~`';
      const result = LexicalMarkdownParser.markdownToLexical(specialOnly);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle mixed valid and invalid markdown', () => {
      const mixedContent = `# Valid Heading

Valid paragraph with **bold** text.

\`\`\`javascript
// Valid code block
console.log("hello");
\`\`\`

Invalid unclosed *emphasis

![Valid image](https://example.com/image.jpg)

![Invalid image](

Valid list:
- Item 1
- Item 2

Invalid unclosed **strong

[Valid link](https://example.com)

[Invalid link](`;

      const result = LexicalMarkdownParser.markdownToLexical(mixedContent);
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(5);
    });
  });

  describe('Fallback Behavior', () => {
    it('should provide meaningful fallbacks for unknown content', () => {
      // Test with preserve unknown nodes enabled
      const options = {
        context: {
          preserveUnknownNodes: true,
          enableGhostFeatures: true,
          fallbackToHTML: true
        }
      };

      const markdown = 'Some content that might have issues';
      const result = LexicalMarkdownParser.markdownToLexical(markdown, options);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle conversion failures gracefully', () => {
      // Create a scenario that might cause conversion issues
      const problematicMarkdown = '```\nUnclosed code\n**Unclosed bold\n[Unclosed link(';
      const result = LexicalMarkdownParser.markdownToLexical(problematicMarkdown);
      
      expect(result.success).toBe(true);
      // Should have some content even if not perfectly parsed
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });
  });
});
